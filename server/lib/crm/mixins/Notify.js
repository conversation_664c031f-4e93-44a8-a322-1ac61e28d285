/**
 *  @module Mixin:Notify - send push notifications to Perkd app
 */
const { Settings } = require('@crm/types'),
	i18n = appRequire('lib/common/i18nLib')

const { LOCALE } = Settings.Name,
	ENGLISH = 'en'

module.exports = function(Model) {

	/**
	 * Send Push Notification to membership card
	 * @param {Membership} membership
	 * @param {Object} template - { name, widget? }
	 * @param {Object} [personalize] data
	 * @param {Object} [options] - { image, payload }
	 */
	Model.appNotify = async function (membership, template, personalize = {}, options = {}) {
		const { app } = Model,
			{ Perkd } = appModule('perkd'),
			{ digitalCard } = membership ?? {},
			{ name, widget } = template ?? {},
			{ id } = digitalCard ?? {},
			{ image, ...rest } = options

		if (!id || !name) return

		const cardId = String(id),
			{ languages = [] } = app.getSettings(LOCALE),
			[ language = ENGLISH ] = languages,
			content = i18n.translate(
				{ body: name },
				{ title: 'title' },
				{ globalize: { t: {}, default: language } },
				{ ...personalize }
			),
			opt = {
				...rest,
				payload: {
					nav: {
						route: [ { card: { id } } ]
					},
					options: { banner: true },
				},
			}

		if (image) {
			for (const l of Object.keys(content)) {
				content[l].image = image
			}
		}
		if (widget) {
			opt.payload.nav.route.push({ [widget]: {} })
		}

		try {
			await Perkd.notify.send(cardId, content, opt)
		}
		catch (err) {
			appNotify(`[${Model.name}]appNotify`, { cardId, err }, 'error')
		}
	}

	/**
	 * Send Push Notification to message
	 * @param {String} messageId
	 * @param {String} template
	 * @param {Object} [personalize] data
	 * @param {Object} [options] - { image, payload }
	 */
	Model.messageNotify = async function (messageId, template, personalize, options) {
		const { Perkd } = appModule('perkd'),
			{ app } = Model,
			{ languages = [] } = app.getSettings(LOCALE),
			[ language = ENGLISH ] = languages,
			content = i18n.translate(
				{ body: template },
				{ title: 'title' },
				{ globalize: { t: {}, default: language } },
				{ ...personalize }
			),
			notification = { content }

		try {
			await Perkd.messages.notify(messageId, notification, options)
		}
		catch (err) {
			appNotify(`[${Model.name}]messageNotify`, { messageId, template, personalize, err }, 'error')
		}
	}
}
