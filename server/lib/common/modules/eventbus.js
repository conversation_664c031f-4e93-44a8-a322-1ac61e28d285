/**
 *  @module EventBus
 */
const pLimit = require('p-limit'),
	{ Bus, Utils } = require('@perkd/eventbus'),
	{ Context } = require('@perkd/multitenant-context'),
	{ merge, isEmptyObj, shortId, camelcase, findPath, cloneDeep } = require('@perkd/utils')

const { deepObjectValues, match } = Utils,
	NO_PUB_SUB = { subscribe: [], publish: [] },
	rateLimit = pLimit(500)	// concurrency

class EventBus extends Bus {
	/**
	 * @param	{Object} app - emitter
	 * @param	{Object} options
	 * 			{String} definitions - relative file path to event registry
	 * 			{Boolean} enabled
	 * 			{Object} pub - { enabled, log }
	 * 			{Object} sub - { enabled, log }
	 * 			{Object} tenant - events
	 * 				{String[]} subscribe - list of (remote) event name/pattern to listen from Eventbus
	 * 				{String[]} publish - list of (local) event name/pattern to be published onto Eventbus
	 * 			{Object[]} mapping - mapping of (subscribed) event name to alias (i.e. [{ from, to }])
	 * redis config, override defaults:
	 * 			{String} host
	 * 			{Number} port
	 * 			{Number} MAXLEN
	 * 			{Number} LIMIT
	 * 			{Object} limitConsumer { tokensPerInterval, interval }
	 * 			{Object} limitBus { tokensPerInterval, interval }
	 */
	constructor(app, options) {
		const { enabled, host, port, username, password, pub, sub, tenant = NO_PUB_SUB, mapping } = options,
			{ MAXLEN, LIMIT, limitConsumer, limitBus } = options,
			service = { name: app.service.name.toLowerCase() },
			settings = { service, tenant, mapping },
			config = {
				redis: { host, port, username, password }
			}

		if (MAXLEN !== undefined) config.MAXLEN = MAXLEN
		if (LIMIT !== undefined) config.LIMIT = LIMIT
		if (limitConsumer !== undefined) config.limitConsumer = limitConsumer
		if (limitBus !== undefined) config.limitBus = limitBus

		super(app, settings, config)

		this._settings = settings
		this._config = { enabled, pub, sub }

		this.registry = deepObjectValues(app.EventRegistry)
		this.multitenancy = app.service.multitenancy

		// ---  Globals: this.app, Events & Metrics
		this.app = app
		this.domain = camelcase(app.service.domain.trim()).toLowerCase()

		/*
		{
			"person.person.updated": {
				"mytenant": [{ "handler": "person.person.updated", "count": 1 }],
				"...": "..."
			},
			"sales.order.paid": {
				"mytenant": [{ "handler": [Metrics], "filter": null, "count", 1 }, { "handler": [Behavior], "filter": null, "count", 1 }],
				"...": "..."
			}
		}
		*/
		this.subEventsListeners = {}
	}

	/**
	 * Connect to EventBus then create Consumer
	 */
	async init() {
		const { app, _settings, _config } = this,
			{ enabled } = _config,
			subscribe = [ ..._settings.tenant.subscribe ],
			publish = this.getMatchedEventNames([ ..._settings.tenant.publish ]),
			events = [ ...subscribe, ...publish ],
			tenants = app.allTenantCodes()
		let eventRegistry = {}

		if (!enabled) return

		// Add to app.Event
		for (const event of events) {
			eventRegistry = merge(eventRegistry, findPath(app.EventRegistry, event))
		}
		this.app.Event = merge(this.app.Event, eventRegistry)

		await Bus.prototype.init.call(this, tenants)
	}

	/**
	 * Create Event Stream then enable sub & pub events
	 */
	async start() {
		const { app, tenants, _settings, _config } = this,
			{ service, Event } = app,
			{ tenant, mapping = [] } = _settings,
			{ enabled, pub = {}, sub = {} } = _config,
			{ subscribe = [], publish = [] } = tenant,
			toPublish = this.getMatchedEventNames(publish)

		if (!enabled) return

		const subMapped = subscribe.map(sub => mapping.find(map => map.from === sub)?.to ?? sub),
			setSub = new Set(subMapped),
			intersect = toPublish.filter(event => setSub.has(event))

		if (intersect.length > 0) {
			return Promise.reject(`[eventbus] Eventbus Start Failed. Events can't both be published and subscribed to. \n ${intersect}`)
		}

		if (pub.enabled) {
			for (const event of toPublish) {
				Bus.prototype.publish.call(this, event)
			}
		}
		else {
			appEcho('[eventbus] ⚠️ tenant events pubClient disabled')
		}

		if (sub.enabled) {
			const subscribes = []
			for (const tenantCode of tenants) {
				subscribes.push(rateLimit(() => this.batchSubscribe(subscribe, tenantCode)))
			}
			await Promise.all(subscribes)
		}
		else {
			appEcho('[eventbus] ⚠️ tenant events subClient disabled')
		}

		await Bus.prototype.start.call(this)
		app.emit(Event.eventbus.STARTED)
	}

	async terminate() {
		const { app } = this,
			{ Event } = app

		await this.end()
		app.emit(Event.eventbus.STOPPED)
	}

	/**
	 * Pub event onto EventBus
	 * @param	{String} name
	 * @param	{Object} body
	 * @param	{String} [tenantCode]
	 * @return	{Promise<String>} event id
	 */
	publish(name, body = {}, tenantCode) {
		const { app, _config, multitenancy } = this,
			{ service } = app,
			{ pub = {} } = _config,
			tenant = tenantCode || (multitenancy ? Context.tenant : service.tenantCode),
			data = { ...(typeof body.toJSON === 'function' ? body.toJSON() : body) },
			metadata = { timezone: Context.timezone, timestamp: Date.now() }

		if (pub.log) {
			console.log('[eventbus]', { type: 'pub', tenant, name, data, metadata })
		}
		return Bus.prototype.putEvent.call(this, name, data, tenant, metadata)
	}

	/**
	 * Subscribe to event(s) for a tenant
	 * @param	{String} name
	 * @param	{String} tenantCode
	 * @param	{Function} handler
	 * @return	{Promise<String|void>} subscribed event
	 */
	async subscribe(name, tenantCode, handler) {
		if (!this.subEnabled()) return

		if (Array.isArray(name)) { // TODO: temp, to remove @wilson
			appNotify('eventbus_wrong_subscribe_param', name, 'error')
			return
		}

		this.subEventsListeners[name] = this.subEventsListeners[name] || {}

		const listeners = this.subEventsListeners[name][tenantCode] = this.subEventsListeners[name][tenantCode] || [],
			toSubscribe = listeners.length === 0

		let i = 0
		for (i = 0; i < listeners.length; i++) {
			if (listeners[i].handler === handler) {
				listeners[i].count++
				break
			}
		}

		if (i >= listeners.length) { // 1st subscription
			listeners.push({ handler, count: 1 })
		}

		return (toSubscribe) ? Bus.prototype.subscribe.call(this, name, tenantCode).then(() => name) : name
	}

	/**
	 * Subscribe to multiple events at once for a tenant
	 * @param	{String[]} eventNames - Array of event names to subscribe to
	 * @param	{String} tenantCode - Tenant code
	 * @param	{Function} handler - Optional event handler
	 * @return	{Promise<String[]>} subscribed events
	 */
	async batchSubscribe(eventNames, tenantCode, handler) {
		if (!this.subEnabled()) return []

		if (!Array.isArray(eventNames)) {
			appNotify('eventbus_wrong_batchSubscribe_param', eventNames, 'error')
			return []
		}

		// If no events to subscribe, return empty array
		if (eventNames.length === 0) return []

		// Setup listeners for each event
		const toSubscribe = []

		for (const name of eventNames) {
			this.subEventsListeners[name] = this.subEventsListeners[name] || {}

			const listeners = this.subEventsListeners[name][tenantCode] = this.subEventsListeners[name][tenantCode] || []
			let i = 0
			for (i = 0; i < listeners.length; i++) {
				if (listeners[i].handler === handler) {
					listeners[i].count++
					break
				}
			}

			if (i >= listeners.length) { // 1st subscription
				listeners.push({ handler, count: 1 })
			}

			// If this is the first listener for this event and tenant, mark for subscription
			if (listeners.length > 0 && !handler) {
				// When no handler is provided, we're just setting up the subscription
				// This is used during service startup to subscribe to all events
			}

			// Add to list of events to subscribe via batchSubscribe
			toSubscribe.push(name)
		}

		// Use the parent Bus batchSubscribe method to efficiently subscribe to all events at once
		if (toSubscribe.length > 0) {
			await Bus.prototype.batchSubscribe.call(this, toSubscribe, tenantCode)
		}

		return toSubscribe
	}

	async unsubscribe(name, tenantCode, handler) {
		this.subEventsListeners[name] = this.subEventsListeners[name] || {}

		if (!this.subEnabled()) return false

		const listeners = this.subEventsListeners[name][tenantCode] = this.subEventsListeners[name][tenantCode] || []

		for (let i = 0; i < listeners.length; i++) {
			const listener = listeners[i]

			if (listener.handler === handler && --listener.count <= 0) {
				listeners.splice(i, 1)
				break
			}
		}

		if (listeners.length === 0) {
			delete this.subEventsListeners[name][tenantCode]

			if (isEmptyObj(this.subEventsListeners[name])) {
				delete this.subEventsListeners[name]
				await Bus.prototype.unsubscribe.call(this, name, tenantCode)
			}
		}
		return false
	}

	async unsubAllTenants(eventName) {
		const { tenants } = this

		if (!this.subEnabled()) return

		// tenant events - run in parallel
		await Promise.all(
			Array.from(tenants).map(tenant =>
				this.unsubscribe(eventName, tenant)
			)
		)
	}

	async pause() {
		const { tenants } = this

		if (!this._config.enabled) return

		// tenant events - run in parallel
		await Promise.all(
			Array.from(tenants).map(tenant =>
				Bus.prototype.pause.call(this, tenant)
			)
		)
	}

	async resume() {
		const { tenants } = this

		if (!this._config.enabled) return

		// tenant events - run in parallel
		await Promise.all(
			Array.from(tenants).map(tenant =>
				Bus.prototype.resume.call(this, tenant)
			)
		)
	}

	createEvent(eventName, eventData, tenantCode) {
		const { app, multitenancy } = this,
			{ timezone } = Context,
			name = eventName.trim(),
			names = name.split('.'),
			domain = names.shift(),
			actor = names.shift(),
			action = names.join('.'),
			data = typeof eventData.toJSON === 'function' ? eventData.toJSON() : eventData

		return {
			id: shortId(),
			name,
			domain,
			actor,
			action,
			data,
			timezone: multitenancy ? timezone : 'Asia/Singapore',
			timestamp: Date.now(),
			tenantCode: tenantCode || (multitenancy ? Context.tenant : app.service.tenantCode)
		}
	}

	getSubscribeStatus(tenantCode, eventName) {
		const listeners = cloneDeep(this.subEventsListeners),
			subscribed = {}

		for (const evt of Object.keys(listeners)) {
			if (eventName && evt !== eventName) continue

			for (const tenant of Object.keys(listeners[evt])) {
				if (tenantCode && tenant !== tenantCode) continue
				subscribed[tenant] = subscribed[tenant] || {}
				subscribed[tenant][evt] = listeners[evt][tenant].length
			}
		}
		return subscribed
	}

	// -----  Private functions  -----

	getMatchedEventNames(events) {
		const { registry } = this

		return registry.reduce((list, name) => {
			const event = events.find(pattern => match(name, pattern))
			if (event) list.push(name)
			return list
		}, [])
	}

	// Emit event with emitter (app)
	async invokeListeners(name, data, tenant, metadata) {
		const { app, _config, subEventsListeners } = this,
			{ Event } = app.models,
			{ sub = {} } = _config,
			{ name: eventName, timezone } = metadata,
			subscribed = subEventsListeners[eventName] || subEventsListeners[name]

		if (!eventName || !subscribed) {
			console.log('invalid event', { name, data, tenant, metadata })
			return undefined
		}

		const listeners = subscribed[tenant] || [],
			event = buildv3Event(data, tenant, metadata) // backward compat for LB3

		return Context.runAsTenant(tenant, async () => {
			if (timezone) Context.timezone = timezone
			for (const listener of listeners) {
				try {
					if (typeof listener.handler === 'function') {
						Promise.resolve(listener.handler(event, tenant, metadata))
							.catch(err => handlerError(err, event, tenant))
					}
					else if (listener.handler && typeof listener.handler.emit === 'function') {
						Promise.resolve(listener.handler.emit(name, event, tenant, metadata))
							.catch(err => handlerError(err, event, tenant))
					}
					else {
						app.emit(name, event, tenant, metadata)
					}
				}
				catch (err) {
					handlerError(err, event, tenant)
				}
			}

			if (sub.log) {
				Event.create({ type: 'sub', tenant, name, data, metadata })
			}
		}, app.connectionManager)
	}

	pubEnabled() {
		return this._config.enabled && this._config.pub.enabled
	}

	subEnabled() {
		return this._config.enabled && this._config.sub.enabled
	}
}

EventBus.prototype.putEvent = EventBus.prototype.publish

module.exports = EventBus

function buildv3Event(data, tenantCode, metadata) {
	const { id, domain, actor, action, name, timezone } = metadata
	return { id, domain, actor, action, name, data, tenantCode, timezone }
}

function handlerError(err, event, tenant) {
	console.error('[EventBus] Handler', {
		event,
		tenant,
		error: err.stack
	})
	// Consider adding metrics/appNotify here
}
